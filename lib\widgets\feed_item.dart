import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post_model.dart';
import '../providers/posts_provider.dart';
import '../theme/app_theme.dart';
import '../screens/post_detail_screen.dart';
import 'video_player_widget.dart';

class FeedItem extends StatefulWidget {
  final PostModel post;
  final bool isVisible;

  const FeedItem({super.key, required this.post, this.isVisible = true});

  @override
  State<FeedItem> createState() => _FeedItemState();
}

class _FeedItemState extends State<FeedItem> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Stack(
        children: [
          // Background media content
          _buildMediaContent(),

          // Minimal gradient overlay only for text readability (videos only)
          if (widget.post.isVideo)
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.transparent,
                    Colors.black.withValues(
                      alpha: 51,
                    ), // 0.2 opacity - minimal for videos
                  ],
                  stops: const [
                    0.0,
                    0.9,
                    1.0,
                  ], // Start very late to preserve video area
                ),
              ),
            ),
          // No gradient overlay for images to show full image

          // Bottom action buttons (horizontal layout)
          Positioned(
            left: 16,
            right: 16,
            bottom: 80, // Moved closer to bottom navigation bar
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Left side - More options (3-dot menu)
                _buildActionButton(
                  icon: Icons.more_vert,
                  label: '',
                  color: Colors.white,
                  onPressed: () => _showPostOptions(context),
                ),

                // Right side - Comments, Likes, Reflex
                Row(
                  children: [
                    _buildActionButton(
                      icon: Icons.chat_bubble_outline,
                      label: widget.post.commentCount.toString(),
                      color: Colors.white,
                      onPressed: () => _handleComment(context),
                    ),
                    const SizedBox(width: 24),
                    _buildActionButton(
                      icon:
                          widget.post.isLikedByCurrentUser
                              ? Icons.shield
                              : Icons.shield_outlined,
                      label: widget.post.likeCount.toString(),
                      color:
                          widget.post.isLikedByCurrentUser
                              ? AppColors.gfGreen
                              : Colors.white,
                      onPressed: () => _handleLike(context),
                    ),
                    const SizedBox(width: 24),
                    _buildActionButton(
                      icon:
                          Icons
                              .sports_martial_arts, // Sword-like icon for Reflex
                      label: 'Reflex',
                      color: Colors.white,
                      onPressed: () => _handleReflex(context),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Top left content info
          Positioned(
            left: 16,
            right: 80,
            top: 40, // Moved higher up with less margin
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // User info
                Row(
                  children: [
                    _buildAvatar(),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.post.authorDisplayName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              fontSize: 16,
                              shadows: [
                                Shadow(
                                  color: Colors.black,
                                  blurRadius: 2,
                                  offset: Offset(1, 1),
                                ),
                              ],
                            ),
                          ),
                          Text(
                            '@${widget.post.authorUsername}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                              shadows: [
                                Shadow(
                                  color: Colors.black,
                                  blurRadius: 2,
                                  offset: Offset(1, 1),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Post content (smaller text)
                if (widget.post.content.isNotEmpty)
                  Text(
                    widget.post.content,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 13, // Reduced from 16 to 13
                      height: 1.3,
                      shadows: [
                        Shadow(
                          color: Colors.black,
                          blurRadius: 2,
                          offset: Offset(1, 1),
                        ),
                      ],
                    ),
                    maxLines: 2, // Reduced from 3 to 2 lines
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMediaContent() {
    if (widget.post.hasMedia && widget.post.mediaUrl != null) {
      if (widget.post.isImage) {
        return SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: Image.network(
            widget.post.mediaUrl!,
            fit: BoxFit.cover, // Changed from contain to cover for full screen
            errorBuilder: (context, error, stackTrace) {
              if (kDebugMode) {
                print('Error loading image: $error');
              }
              return _buildMediaPlaceholder('Image');
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                  valueColor: const AlwaysStoppedAnimation<Color>(
                    AppColors.gfGreen,
                  ),
                ),
              );
            },
          ),
        );
      } else if (widget.post.isVideo) {
        // Use the video player widget
        return VideoPlayerWidget(
          videoUrl: widget.post.mediaUrl!,
          autoPlay: true,
          isVisible: widget.isVisible,
        );
      }
    }

    // Fallback to a gradient background
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
    );
  }

  Widget _buildMediaPlaceholder(String type) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [AppColors.gfTeal, AppColors.gfDarkBlue],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == 'Video' ? Icons.play_circle_outline : Icons.image,
              size: 64,
              color: Colors.white70,
            ),
            const SizedBox(height: 16),
            Text(
              '$type Content',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    if (widget.post.avatarUrl != null && widget.post.avatarUrl!.isNotEmpty) {
      return CircleAvatar(
        backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
        radius: 20,
        backgroundImage: NetworkImage(widget.post.avatarUrl!),
        onBackgroundImageError: (_, __) {},
        child:
            widget.post.avatarUrl!.isEmpty
                ? Text(
                  widget.post.authorDisplayName.isNotEmpty
                      ? widget.post.authorDisplayName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: AppColors.gfDarkBlue,
                    fontWeight: FontWeight.bold,
                  ),
                )
                : null,
      );
    }

    return CircleAvatar(
      backgroundColor: AppColors.gfGreen.withValues(alpha: 77), // 0.3 opacity
      radius: 20,
      child: Text(
        widget.post.authorDisplayName.isNotEmpty
            ? widget.post.authorDisplayName[0].toUpperCase()
            : 'U',
        style: const TextStyle(
          color: AppColors.gfDarkBlue,
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: const Color.fromARGB(200, 96, 96, 96),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          if (label.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w800,
                shadows: [
                  Shadow(
                    color: Colors.black,
                    blurRadius: 2,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _handleLike(BuildContext context) async {
    final postsProvider = Provider.of<PostsProvider>(context, listen: false);
    await postsProvider.toggleLike(widget.post.id);
  }

  void _handleComment(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: widget.post),
      ),
    );
  }

  void _handleReflex(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Reflex feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
      ),
    );
  }

  void _handleShare(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share feature coming soon!'),
        backgroundColor: AppColors.gfTeal,
      ),
    );
  }

  void _showPostOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.gfDarkBackground,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.share, color: AppColors.gfOffWhite),
                  title: const Text(
                    'Share Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _handleShare(context);
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.bookmark_border,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Save Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Save feature coming soon!'),
                      ),
                    );
                  },
                ),
                ListTile(
                  leading: const Icon(
                    Icons.report_outlined,
                    color: AppColors.gfOffWhite,
                  ),
                  title: const Text(
                    'Report Post',
                    style: TextStyle(color: AppColors.gfOffWhite),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Report feature coming soon!'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
    );
  }
}
